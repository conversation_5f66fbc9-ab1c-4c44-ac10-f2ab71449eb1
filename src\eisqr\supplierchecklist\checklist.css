/* Supplier Checklist Styles - Matching test.js design */

/* Utility classes to match Tailwind CSS */
.min-h-screen {
    min-height: 100vh;
}

.bg-gradient-to-br {
    background: linear-gradient(to bottom right, #f8fafc, #e2e8f0);
}

.from-slate-50 {
    --tw-gradient-from: #f8fafc;
}

.to-slate-100 {
    --tw-gradient-to: #f1f5f9;
}

.p-6 {
    padding: 1.5rem;
}

.max-w-7xl {
    max-width: 80rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.mb-8 {
    margin-bottom: 2rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
}

.font-bold {
    font-weight: 700;
}

.text-foreground {
    color: #0f172a;
}

.text-muted-foreground {
    color: #64748b;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-6 {
    gap: 1.5rem;
}

.bg-white {
    background-color: #ffffff;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Background colors */
.bg-blue-50 {
    background-color: #eff6ff;
}

.bg-green-50 {
    background-color: #f0fdf4;
}

.bg-amber-50 {
    background-color: #fffbeb;
}

/* Border colors */
.border-blue-200 {
    border-color: #bfdbfe;
}

.border-green-200 {
    border-color: #bbf7d0;
}

.border-amber-200 {
    border-color: #fde68a;
}

/* Responsive grid */
@media (min-width: 1024px) {
    .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

/* Height calculation */
.h-\[calc\(100vh-200px\)\] {
    height: calc(100vh - 200px);
}

/* Custom Column Widget Styles */
.custom-column-widget {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: 0.75rem;
    border: 2px solid;
    padding: 1.5rem;
    background-color: white;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.custom-column-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.custom-column-widget.disabled {
    opacity: 0.6;
    pointer-events: none;
}

.widget-header {
    margin-bottom: 1.5rem;
}

.widget-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 1rem 0;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    z-index: 10;
}

.search-field {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.search-field:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.add-btn {
    width: 100%;
    justify-content: center;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.add-section {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.add-field {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.save-btn,
.cancel-btn {
    padding: 0.5rem;
    border-radius: 0.375rem;
    min-width: 2.5rem;
}

.widget-content {
    flex: 1;
    overflow-y: auto;
    padding-right: 0.25rem;
}

.empty-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #9ca3af;
    font-style: italic;
}

.loading-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6b7280;
    gap: 1rem;
}

.loading-message p {
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
}

.items-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Custom Item Card Styles */
.custom-item-card {
    position: relative;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    background-color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
}

.custom-item-card:hover {
    border-color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.custom-item-card.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.custom-item-card.hovered {
    border-color: #60a5fa;
}

.card-main-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 2.5rem;
}

.card-main-content.with-curate-space {
    padding-right: 8rem;
}

.item-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    line-height: 1.4;
}

.edit-section {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    width: 100%;
}

.edit-field {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.edit-save,
.edit-cancel {
    padding: 0.375rem;
    border-radius: 0.375rem;
    min-width: 2rem;
}

.action-controls {
    position: absolute;
    top: 50%;
    right: 0.75rem;
    transform: translateY(-50%);
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.action-controls.visible {
    opacity: 1;
}

.action-control {
    padding: 0.375rem;
    border-radius: 0.375rem;
    min-width: 2rem;
    height: 2rem;
}

.selection-bar {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: #3b82f6;
    border-radius: 0 2px 2px 0;
}

/* Curate Page Styles */
.curate-page {
    min-height: 100vh;
    background: linear-gradient(to bottom right, #f8fafc, #e2e8f0);
    padding: 1.5rem;
}

.curate-header {
    margin-bottom: 2rem;
}

.curate-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

/* Scrollbar Styling */
.widget-content::-webkit-scrollbar {
    width: 6px;
}

.widget-content::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.widget-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.widget-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Animation for smooth transitions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.custom-item-card {
    animation: fadeIn 0.3s ease-out;
}

/* Focus styles for accessibility */
.search-field:focus,
.add-field:focus,
.edit-field:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Button hover effects */
.add-btn:hover:not(:disabled) {
    background-color: #2563eb;
    transform: translateY(-1px);
}

.action-control:hover {
    transform: scale(1.05);
}

/* Disabled state */
.custom-column-widget.disabled .add-btn {
    opacity: 0.5;
    cursor: not-allowed;
}

.custom-column-widget.disabled .search-field {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Additional Modern Enhancements */
.custom-column-widget {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.custom-item-card {
    backdrop-filter: blur(5px);
    background: rgba(255, 255, 255, 0.98);
}

.widget-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.custom-item-card.selected .item-title {
    color: #1e40af;
    font-weight: 700;
}

/* Improved button styles */
.add-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.add-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Enhanced search field */
.search-field {
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(203, 213, 225, 0.6);
    backdrop-filter: blur(5px);
}

.search-field:focus {
    background: rgba(255, 255, 255, 0.95);
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Improved action buttons */
.action-control {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(203, 213, 225, 0.3);
}

.action-control:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced selection indicator */
.selection-bar {
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    box-shadow: 2px 0 8px rgba(102, 126, 234, 0.3);
}

/* Smooth loading animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.empty-message {
    animation: pulse 2s infinite;
}

/* Enhanced hover effects */
.custom-column-widget:hover {
    transform: translateY(-3px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.custom-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Improved typography */
.item-title {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: 0.3px;
}

.widget-title {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: 0.5px;
}

/* Curation Modal Styles - Matching test.js design */
.curate-modal .p-dialog-content {
    padding: 0;
    height: calc(90vh - 180px);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.curate-modal .p-dialog-footer {
    padding: 0;
    border-top: none;
    background: transparent;
}

.curate-modal-content {
    display: flex;
    flex: 1;
    gap: 0;
    overflow: hidden;
    min-height: 0;
}

/* Left Sidebar - Components */
.components-sidebar {
    width: 320px;
    background-color: white;
    border-right: 1px solid #e5e7eb;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    margin-bottom: 1rem;
}

.sidebar-title-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.sidebar-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
}

.components-count {
    font-size: 0.875rem;
    color: #6b7280;
    background-color: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.sidebar-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
}

.components-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    overflow-y: auto;
}

.component-item {
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: white;
}

.component-item:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
}

.component-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.component-icon {
    width: 2rem;
    height: 2rem;
    background-color: #dbeafe;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2563eb;
    font-weight: 500;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.component-info {
    flex: 1;
    min-width: 0;
}

.component-title {
    font-weight: 500;
    color: #111827;
    font-size: 0.875rem;
    margin: 0 0 0.25rem 0;
}

.component-description {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
}

/* Main Canvas Area */
.canvas-area {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #f9fafb;
}

.canvas-container {
    /* max-width: 64rem; */
    /* margin: 0 auto; */
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.canvas-content {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.empty-canvas {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    color: #6b7280;
    padding: 2rem;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #d1d5db;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0 0 0.5rem 0;
    color: #374151;
}

.empty-description {
    font-size: 0.875rem;
    margin: 0;
    text-align: center;
}

.components-canvas {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow-y: auto;
    flex: 1;
}

.canvas-component {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: white;
    transition: all 0.2s ease;
}

.canvas-component:hover {
    border-color: #3b82f6;
}

.component-header {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.component-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.component-grip {
    color: #9ca3af;
    font-size: 1rem;
}

.component-type-icon {
    width: 1.5rem;
    height: 1.5rem;
    background-color: #dbeafe;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2563eb;
    font-size: 0.75rem;
}

.component-details {
    flex: 1;
}

.component-title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.component-name {
    font-weight: 500;
    color: #111827;
}

.component-badges {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.content-badge {
    background-color: #dbeafe;
    color: #1e40af;
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
}

.required-badge {
    background-color: #fef3c7;
    color: #92400e;
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    border: 1px solid #fbbf24;
}

.component-body {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.component-text {
    font-size: 0.875rem;
    color: #6b7280;
    font-style: italic;
}

.questions-preview {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.questions-count {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.question-preview {
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.75rem;
    background-color: #f9fafb;
}

.question-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.question-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.question-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.question-type {
    font-size: 0.75rem;
    background-color: #e5e7eb;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
}

.question-score {
    font-size: 0.75rem;
    color: #6b7280;
}

.question-preview-content {
    margin-top: 0.5rem;
}

.radio-preview {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.radio-option {
    font-size: 0.875rem;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.preview-placeholder {
    font-size: 0.875rem;
    color: #6b7280;
    font-style: italic;
}

.component-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.canvas-component:hover .component-actions {
    opacity: 1;
}

/* Footer */
.curate-footer {
    border-top: 1px solid #e5e7eb;
    padding: 1rem;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-shrink: 0;
    min-height: 80px;
}

.footer-left {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.footer-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    flex-wrap: wrap;
}

.component-count {
    font-weight: 500;
    color: #374151;
}

.question-count {
    color: #6b7280;
}

.unsaved-indicator {
    color: #f59e0b;
    font-weight: 500;
}

.footer-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

/* Loading state for save button */
.p-button.p-button-loading .p-button-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive footer */
@media (max-width: 768px) {
    .curate-footer {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .footer-left {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .footer-info {
        justify-content: center;
    }

    .footer-actions {
        justify-content: center;
    }
}

/* Dialog Styles for Edit Components */
.checkpoint-dialog .p-dialog-content,
.header-dialog .p-dialog-content {
    padding: 1.5rem;
}

.checkpoint-dialog-content,
.header-dialog-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.field label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.w-full {
    width: 100%;
}

.questions-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.questions-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.questions-header label {
    font-weight: 600;
    color: #111827;
    font-size: 1rem;
}

.question-item {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #f9fafb;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.question-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.question-number-field {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.question-number-field label {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;
}

.question-config {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0.75rem;
    align-items: end;
}

.marks-field {
    grid-column: 1 / -1;
}

.marks-field small {
    color: #6b7280;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.no-scoring {
    grid-column: 1 / -1;
    text-align: center;
}

.no-scoring small {
    color: #6b7280;
    font-style: italic;
}

.required-section {
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

.required-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.required-label {
    font-weight: 500;
    color: #1e40af;
    cursor: pointer;
    margin: 0;
}

.required-description {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
    margin-left: 2rem;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
    margin-top: 1rem;
}

.preview-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    text-align: center;
}

.preview-success {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.preview-success h4 {
    color: #059669;
    margin: 0;
}

.preview-text {
    background-color: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

.preview-text h5 {
    margin: 0 0 0.5rem 0;
    color: #0c4a6e;
}

.preview-text p {
    margin: 0;
    color: #075985;
    font-size: 0.875rem;
}

.keyboard-hint {
    color: #6b7280;
    font-size: 0.75rem;
    text-align: center;
    margin-top: 0.5rem;
}

/* Responsive adjustments for dialogs */
@media (max-width: 768px) {
    .checkpoint-dialog {
        width: 95vw !important;
    }

    .question-config {
        grid-template-columns: 1fr;
    }

    .header-dialog {
        width: 95vw !important;
    }
}
