import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Trash2, Plus } from 'lucide-react';

interface Question {
  id: string;
  text?: string;
  type: 'A' | 'B' | 'C' | 'D' | 'E';
  questionNumber: string;
  numerator?: number;
  denominator?: number;
  marks?: string;
}

interface EditCheckpointDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  initialData?: {
    title: string;
    description: string;
    questions: Question[];
    required: boolean;
  };
  checkpointGroupIndex?: number;
}

export function EditCheckpointDialog({ 
  isOpen, 
  onClose, 
  onSave, 
  initialData,
  checkpointGroupIndex = 1 
}: EditCheckpointDialogProps) {
  const [title, setTitle] = useState(initialData?.title || '');
  const [description, setDescription] = useState(initialData?.description || '');
  const [required, setRequired] = useState(initialData?.required || false);
  const [questions, setQuestions] = useState<Question[]>(
    initialData?.questions || [
      { id: '1', text: '', type: 'A', questionNumber: '1.1.1', numerator: 1, denominator: 1 },
      { id: '2', text: '', type: 'A', questionNumber: '1.1.2', numerator: 1, denominator: 1 }
    ]
  );

  const addQuestion = () => {
    const newQuestion: Question = {
      id: Date.now().toString(),
      text: '',
      type: 'A',
      questionNumber: '',
      numerator: 1,
      denominator: 1
    };
    setQuestions(prev => [...prev, newQuestion]);
  };

  const removeQuestion = (id: string) => {
    setQuestions(prev => prev.filter(q => q.id !== id));
  };

  const updateQuestion = (id: string, field: keyof Question, value: any) => {
    setQuestions(prev => prev.map(q => {
      if (q.id === id) {
        const updatedQuestion = { ...q, [field]: value };
        
        // Handle response type changes
        if (field === 'type') {
          if (value === 'C') {
            // For Type C, remove numerator/denominator and add marks
            delete updatedQuestion.numerator;
            delete updatedQuestion.denominator;
            updatedQuestion.marks = '';
          } else if (value === 'D' || value === 'E') {
            // For Types D and E, remove all scoring fields
            delete updatedQuestion.numerator;
            delete updatedQuestion.denominator;
            delete updatedQuestion.marks;
          } else {
            // For Types A and B, ensure numerator/denominator exist
            delete updatedQuestion.marks;
            updatedQuestion.numerator = updatedQuestion.numerator || 1;
            updatedQuestion.denominator = updatedQuestion.denominator || 1;
          }
        }
        
        return updatedQuestion;
      }
      return q;
    }));
  };

  const handleSave = () => {
    onSave({
      title,
      description,
      questions,
      required
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Checkpoint Group</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Group Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Group Title</Label>
            <Input
              id="title"
              placeholder="Enter checkpoint group title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </div>

          {/* Group Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Group Description (Optional)</Label>
            <Textarea
              id="description"
              placeholder="Enter group description or instructions"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>

          {/* Questions Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Questions</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addQuestion}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Question
              </Button>
            </div>

            {questions.map((question, index) => (
              <div key={question.id} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Label>Question Number:</Label>
                    <Input
                      placeholder="e.g., 1.1.1"
                      value={question.questionNumber}
                      onChange={(e) => updateQuestion(question.id, 'questionNumber', e.target.value)}
                      className="w-20"
                    />
                  </div>
                  {questions.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeQuestion(question.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <Input
                  placeholder="Enter your question"
                  value={question.text || ''}
                  onChange={(e) => updateQuestion(question.id, 'text', e.target.value)}
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Response Type */}
                  <div className="space-y-2">
                    <Label>Response Type</Label>
                    <Select
                      value={question.type}
                      onValueChange={(value: 'A' | 'B' | 'C' | 'D' | 'E') => 
                        updateQuestion(question.id, 'type', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="A">A (Yes/No/NA)</SelectItem>
                        <SelectItem value="B">B (Yes/No)</SelectItem>
                        <SelectItem value="C">C (% selector)</SelectItem>
                        <SelectItem value="D">D (Description)</SelectItem>
                        <SelectItem value="E">E (Attachment)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Conditional Scoring Fields */}
                  {question.type === 'A' || question.type === 'B' ? (
                    <>
                      {/* Numerator */}
                      <div className="space-y-2">
                        <Label>Numerator</Label>
                        <Input
                          type="number"
                          min="0"
                          value={question.numerator || 0}
                          onChange={(e) => updateQuestion(question.id, 'numerator', parseInt(e.target.value) || 0)}
                        />
                      </div>

                      {/* Denominator */}
                      <div className="space-y-2">
                        <Label>Denominator</Label>
                        <Input
                          type="number"
                          min="1"
                          value={question.denominator || 1}
                          onChange={(e) => updateQuestion(question.id, 'denominator', parseInt(e.target.value) || 1)}
                        />
                      </div>
                    </>
                  ) : question.type === 'C' ? (
                    <div className="col-span-2 space-y-2">
                      <Label>Marks (Percentage-based)</Label>
                      <Input
                        placeholder="e.g., 0% = 0, >50% = 1, >80% = 2"
                        value={question.marks || ''}
                        onChange={(e) => updateQuestion(question.id, 'marks', e.target.value)}
                      />
                      <div className="text-xs text-gray-500">
                        Examples: "0% = 0, {'{>}'}50% = 1, {'{>}'}80% = 2" or "5% = 1, 10% = 2" or "{'{>}'}50% = 1, 100% = 2"
                      </div>
                    </div>
                  ) : (
                    <div className="col-span-2 flex items-center text-sm text-gray-500">
                      No scoring required for this response type
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Required Toggle */}
          <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
            <Switch
              checked={required}
              onCheckedChange={setRequired}
            />
            <Label>Required checkpoint group</Label>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
